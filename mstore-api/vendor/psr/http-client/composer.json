{"name": "psr/http-client", "description": "Common interface for HTTP clients", "keywords": ["psr", "psr-18", "http", "http-client"], "homepage": "https://github.com/php-fig/http-client", "license": "MIT", "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "support": {"source": "https://github.com/php-fig/http-client"}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}}