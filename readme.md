## MStore API WordPress Plugin

Contributors: InspireUI Ltd
License: GPL-2.0
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Download the latest version: https://github.com/inspireui/mstore-api/releases

### How to Install Manually?

1. Extract the zip files, copy folder `mstore-api` to `wp-content/plugins` folder - https://tppr.me/37cOO
2. Activate 'MStore API' through the 'Plugins' menu in WordPress.

### Auto Installation

1. Go to Plugins > Add New in your WordPress admin and search for 'MStore API'.
1. Click Install. Activate 'MStore API' through the 'Plugins' menu in WordPress.

### What is MStore API ?

The plugin is used to configure [Mstore/FluxStore](https://1.envato.market/mKdNe) mobile apps and supports RestAPI to connect to the app.

[youtube https://youtu.be/sYnHhnS5WnQ]

[Fluxstore](https://codecanyon.net/search/fluxstore) are universal e-commerce apps inspired by Flutter framework, made by Google. With the mission of reducing thousands of hours of business spent on designing, developing, and testing a mobile app, Fluxstore comes as a complete solution for optimizing to deliver your app to the market with high productivity and cost efficiency. It could be able to satisfy all of the business requirements including e-commerce functionalities, impressive UX design, and smooth performance on both iOS and Android devices.

If your business already has a website built based on WooCommerce, Shopify, Opencart, Magento, Listeo, My Listing, PrestaShop, Notion, BigCommerce, Strapi, or WordPress then it is easy to integrate with Fluxstore in just a few steps, and quickly release the final app to both App Store and Google Play. The download package includes full source code and many related resources (designs, documents, videos…) that help you install in the smoothest way.

Either you are business people with raising sales ambition or developers with faster mobile application creation needs, Fluxstore provides you solutions.
Faster- Smoother- Closer.

### Reference links

- App demo: [iOS](https://apps.apple.com/us/app/mstore-flutter/id1469772800), [Android](https://github.com/inspireui/fluxstore/releases/download/demo/fluxstore.apk)
- App Builder: [https://fluxbuilder.com](https://fluxbuilder.com)
- Showcase: [https://fluxbuilder.com/showcase](https://fluxbuilder.com/showcase)
- Company Website: [https://inspireui.com](https://inspireui.com)
- [Youtube](https://www.youtube.com/inspireui) - [Facebook](https://www.facebook.com/groups/1401824449973438) - [Document](https://support.inspireui.com/help-center) - [Products](https://1.envato.market/mKdNe)

== Changelog ==
= 3.4.5 =

- Fix security issue when upload config file
- Fix Authenticated Arbitrary File Deletion Vulnerability
- Update to compatible with the latest WordPress 5.8
- Rename files to upgrade Naming Convention.
- Release the MStore API Postman Collection - https://www.getpostman.com/collections/8f9c088b0e5b82b90400

= 3.4.4 =

- Support Tera Wallet

Read more detail from mstore-api/readme.txt
